import { CloseOutlined } from '@ant-design/icons';
import { Popconfirm } from 'antd';
import { useState } from 'react';

export function CloseModalIcon({ changed, onClose }: { changed: boolean; onClose: () => void }) {
  const [open, setOpen] = useState(false);

  const confirm = () => {
    setOpen(false);
    onClose();
  };

  const cancel = () => {
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setOpen(newOpen);
      return;
    }
    // 根据 changed 状态决定是否显示确认提示
    if (changed) {
      setOpen(newOpen); // 显示确认提示
    } else {
      onClose(); // 直接关闭
    }
  };

  return (
    <Popconfirm
      cancelText="取消"
      description="您有未保存的更改，确定要关闭吗？"
      okText="确定"
      open={open}
      title="关闭弹窗"
      onCancel={cancel}
      onConfirm={confirm}
      onOpenChange={handleOpenChange}
    >
      <div style={{ cursor: 'pointer' }}>
        <CloseOutlined />
      </div>
    </Popconfirm>
  );
}
